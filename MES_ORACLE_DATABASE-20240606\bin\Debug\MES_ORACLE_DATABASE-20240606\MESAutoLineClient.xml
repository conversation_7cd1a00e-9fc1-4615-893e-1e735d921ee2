<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MESAutoLineClient</name>
    </assembly>
    <members>
        <member name="T:MESAutoLineClient.AutoLineClient">
            <summary>
            自动线MES客户端
            不要直接 new，通过调用AutoLineClient.Create(服务器地址)创建实例;
            </summary>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.Create(System.String)">
            <summary>
            创建客户端实例
            </summary>
            <param name="serverUrl">服务器地址</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.#ctor(System.String)">
            <summary>
            实例化对象
            </summary>
            <param name="serverUrl">服务器地址</param>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.ReportWorkByBatch(System.String,System.String,System.String,System.String,System.Decimal)">
            <summary>
            批次件合格品报工接口
            </summary>
            <param name="workOrderNo">工单号</param>
            <param name="standardGroupId">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="employeeNo">操作者工号</param>
            <param name="quantity">数量</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.ReportWorkByBatchBad(System.String,System.String,System.String,System.String,System.Decimal,System.String,System.String)">
            <summary>
            批次件不良品报工接口
            </summary>
            <param name="workOrderNo">工单号</param>
            <param name="standardGroupId">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="employeeNo">操作者工号</param>
            <param name="quantity">数量</param>
            <param name="badId">不良项代码</param>
            <param name="remark">不良备注</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.ReportWorkByProduct(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            序列件产品报工接口
            </summary>
            <param name="serialNumber">产品序列号</param>
            <param name="stationCode">工位码</param>
            <param name="employeeNo">操作者工号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="status">产品状态</param>
            <param name="errorType">错误类型</param>
            <param name="errorDesc">错误描述</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.ReportWorkByCapacitorCoreAndContainer(System.String,System.String,System.String,System.String,System.String[],System.String)">
            <summary>
            芯子组装报工
            </summary>
            <param name="lot">批号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位号</param>
            <param name="containerNo">容器号</param>
            <param name="capacitorCoreNoArray">芯子序列号数组</param>
            <param name="employeeNo">操作者工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.CheckToolByLot(System.String,System.String,System.String,System.String)">
            <summary>
            工装验证
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="toolNo">工装码</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.CheckBusbarByLot(System.String,System.String,System.String,System.String)">
            <summary>
            母排验证
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="busbarNo">母排码</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.CheckCapacitorCoreByLot(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            芯子验证
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="containerNo">容器号</param>
            <param name="capacitorCoreNo">芯子序列号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.CheckProductProcedureByContainer(System.String,System.String)">
            <summary>
            通过容器验证产品工序并返回SN码
            </summary>
            <param name="procedureCode">标准工序码</param>
            <param name="containerNo">容器号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindProductAndTool(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            产品绑工装
            </summary>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="serialNumber">产品序列号</param>
            <param name="toolNo">工装码</param>
            <param name="employeeNo">操作工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindProductAndBusbar(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            产品绑母排
            </summary>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="serialNumber">产品序列号</param>
            <param name="busbarNo">母排序列号</param>
            <param name="employeeNo">操作工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindContainerAndToolAndBusbar(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            绑定容器、工装和母排
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="containerNo">容器号</param>
            <param name="toolNo">工装码</param>
            <param name="busbarNo">母排码</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindContainerAndProductList(System.String,System.String[],System.String,System.String)">
            <summary>
            批量绑定容器和产品SN（自动解绑容器）
            </summary>
            <param name="containerNo">容器号</param>
            <param name="productSerialNumber">产品序列号列表</param>
            <param name="stationCode">工位码</param>
            <param name="employeeNo">操作工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindProductAndContainer(System.String,System.String,System.String,System.String)">
            <summary>
            绑定容器和产品SN
            </summary>
            <param name="productSerialNumber">产品序列号</param>
            <param name="containerNo">容器号</param>
            <param name="stationCode">工位码</param>
            <param name="employeeNo">操作工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindProductAndContainerByTool(System.String,System.String,System.String,System.String,System.String[])">
            <summary>
            根据工装绑定产品和容器
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="csn">容器编码</param>
            <param name="wsns">工装编码数组</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindProductAndToolByCapacitorCore(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            根据芯子序列号绑定产品和工装
            </summary>
            <param name="lot">批次号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="stationCode">工位号</param>
            <param name="capacitorCoreNo">芯子序列号</param>
            <param name="toolNo">工装码</param>
            <param name="employeeNo">操作者工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.BindWorkOrderAndImportantMaterial(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            工单绑定关键原材料条码
            </summary>
            <param name="workOrderNo">工单号</param>
            <param name="standardGroupId">标准工序码</param>
            <param name="stationCode">工位码</param>
            <param name="materialBarcode">原材料条码</param>
            <param name="employeeNo">操作者工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.UnbindContainer(System.String,System.String)">
            <summary>
            解绑与容器绑定的所有产品
            </summary>
            <param name="containerNo">容器号</param>
            <param name="employee">操作者工号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetProductSerialNumberByCapacitorCore(System.String)">
            <summary>
            根据芯子序列号查成品序列号
            </summary>
            <param name="capacitorCoreNo">芯子序列号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetProductSerialNumberByTool(System.String)">
            <summary>
            根据工装查成品序列号
            </summary>
            <param name="toolNo">工装码</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetProductSerialNumberByContainer(System.String)">
            <summary>
            根据容器查产品序列号
            </summary>
            <param name="containerNo">容器号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetSerialProductInfo(System.String)">
            <summary>
            根据产品序列号获取产品信息
            </summary>
            <param name="serialNumber">产品序列号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetSerialProductListByContainerNo(System.String)">
            <summary>
            根据容器号获取产品信息列表
            </summary>
            <param name="containerNo">容器号</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetWorkOrder(System.String)">
            <summary>
            获取工单信息
            </summary>
            <param name="workOrderNo"></param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.SendEquipmentParams(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            设备下发参数
            </summary>
            <param name="lot">批次号</param>
            <param name="stationCode">工位号</param>
            <param name="procedureCode">标准工序码</param>
            <param name="employee">操作人工号</param>
            <param name="ignoreEquipmentState">是否忽略设备状态验证</param>
            <returns></returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.QueryCurrentOnWorkListByStationCode(System.String)">
            <summary>
            根据工位查询当前上岗记录
            </summary>
            <param name="stationCode">工位号</param>
            <returns>上岗记录数组</returns>
        </member>
        <member name="M:MESAutoLineClient.AutoLineClient.GetEquipmentSettingParameters(System.String,System.String,System.String)">
            <summary>
            获取设备下发参数
            </summary>
            <param name="equipmentId">设备ID</param>
            <param name="workOrderNo">工单号</param>
            <param name="procedureCode">标准工序码</param>
            <returns></returns>
        </member>
        <member name="T:MESAutoLineClient.Model.DispatchWorkOrder">
            <summary>
            派工单信息
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.DispatchWorkOrder.StandardGroupId">
            <summary>
            标准工序码
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.DispatchWorkOrder.LastReportWorkDate">
            <summary>
            最后一次报工时间
            </summary>
        </member>
        <member name="T:MESAutoLineClient.Model.EquipmentSettingParameter">
            <summary>
            设备下发参数
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.EquipmentId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.WorkOrderNo">
            <summary>
            工单号
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.ProcedureNo">
            <summary>
            PLM工艺路线工序流水号
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.ProcedureName">
            <summary>
            工序名称
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.ParameterName">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.ParameterType">
            <summary>
            参数类型
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.MaxSpec">
            <summary>
            规格上限
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.MinSpec">
            <summary>
            规格下限
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.MaxControl">
            <summary>
            控制上限
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.MinControl">
            <summary>
            控制下限
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.EquipmentSettingParameter.TargetValue">
            <summary>
            目标值
            </summary>
        </member>
        <member name="T:MESAutoLineClient.Model.WorkOrder">
            <summary>
            工单信息
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.WorkOrderNo">
            <summary>
            工单号
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.PartNo">
            <summary>
            物料编码
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.Quantity">
            <summary>
            工单数量
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.GoodQuantity">
            <summary>
            工单良品数
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.BadQuantity">
            <summary>
            工单不良品数
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.DiscardQuantity">
            <summary>
            工单报废数
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.LastCompletedDispatchWorkOrder">
            <summary>
            已完工的最后一个派工单
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Model.WorkOrder.FirstUncompletedDispatchWorkOrder">
            <summary>
            第一个未完工的派工单
            </summary>
        </member>
        <member name="T:MESAutoLineClient.Result`1">
            <summary>
            通用的接口调用结果包装类
            </summary>
            <typeparam name="T">类型参数，类型为 Nothing 的表示该接口不需要返回数据</typeparam>
        </member>
        <member name="P:MESAutoLineClient.Result`1.Success">
            <summary>
            接口调用成功
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Result`1.Message">
            <summary>
            接口调用结果说明
            </summary>
        </member>
        <member name="P:MESAutoLineClient.Result`1.Data">
            <summary>
            接口调用返回数据
            注: 类型为 Nothing 的没有数据，如果需要取值，请检查下 Message
            </summary>
        </member>
        <member name="T:MES.GraphQl.Client.Variables">
            <summary>
            请求参数
            </summary>
        </member>
        <member name="T:MES.GraphQl.Client.GraphQlClient">
            <summary>
            GraphQL 客户端
            </summary>
        </member>
        <member name="M:MES.GraphQl.Client.GraphQlClient.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="apiUrl">API地址</param>
        </member>
        <member name="M:MES.GraphQl.Client.GraphQlClient.Dispose">
            <inheritdoc />
        </member>
        <member name="M:MES.GraphQl.Client.GraphQlClient.Request``1(System.String,MES.GraphQl.Client.Variables)">
            <summary>
            
            </summary>
            <param name="query">查询指令</param>
            <param name="variables">变量</param>
            <typeparam name="T">查询结果类型</typeparam>
            <returns>查询结果</returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="T:MES.Barcode.IProductBarcodeResolver">
            <summary>
            产品序列号解析器
            </summary>
        </member>
        <member name="M:MES.Barcode.IProductBarcodeResolver.Resolve(System.String)">
            <summary>
            解析条码
            </summary>
            <param name="source">源字符串</param>
            <returns>产品SN</returns>
        </member>
        <member name="T:MES.Barcode.RuleBasedProductBarcodeResolver">
            <summary>
            基于规则的产品序列号解析器
            </summary>
        </member>
        <member name="M:MES.Barcode.RuleBasedProductBarcodeResolver.Resolve(System.String)">
            <summary>
            解析条码
            </summary>
            <param name="source">源字符串</param>
            <returns>产品SN</returns>
        </member>
        <member name="T:MES.Barcode.ProductBarcodeResolverFactory">
            <summary>
            解析器工厂
            </summary>
        </member>
        <member name="M:MES.Barcode.ProductBarcodeResolverFactory.RemoteRuleSetBased">
            <summary>
            基于规则的解析器
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
