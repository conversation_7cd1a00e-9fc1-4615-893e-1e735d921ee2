Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PROCEDURENAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and ProcedureName='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PROCEDURENAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and ProcedureName='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-01400: 无法将 NULL 插入 ("SCADA_MES"."MEASURE_SCHEMA_ITEM"."SCHEMA_ID")
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 691
 INSERT ALL INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P1_计算值', 1) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P2_计算值', 2) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P3_计算值', 3) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P4_计算值', 4) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P5_计算值', 5) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P6_计算值', 6) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P7_计算值', 7) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P8_计算值', 8) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P9_计算值', 9) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P10_计算值', 10) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P11_计算值', 11) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_P12_计算值', 12) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'C01_计算值', 13) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P1_计算值', 14) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P2_计算值', 15) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P3_计算值', 16) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P4_计算值', 17) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P5_计算值', 18) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P6_计算值', 19) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P7_计算值', 20) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P8_计算值', 21) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P9_计算值', 22) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_P10_计算值', 23) INTO MEASURE_SCHEMA_ITEM(SCHEMA_ID, SCHEMA_ITEM_NAME, SCHEMA_ITEM_SEQ) VALUES('', 'F01_计算值', 24) SELECT* FROM dual



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PRO_NAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
SELECT count(*) from  MEASURE_EXT_DATA WHERE SCHEMA_ID='' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-01400: 无法将 NULL 插入 ("SCADA_MES"."MEASURE_EXT_DATA"."SCHEMA_ID")
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 691
 INSERT ALL INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P1_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P2_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P2_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P3_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P3_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P4_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P4_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P5_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P5_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P6_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P6_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P7_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P7_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P8_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P8_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P9_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P9_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P10_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P10_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P11_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P11_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P11_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P12_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P12_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_P12_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_计算值','67S0609212','上公差', 0.4) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','C01_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P1_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P2_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P2_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P3_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P3_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P4_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P4_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P5_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P5_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P6_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P6_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P7_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P7_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P8_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P8_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P9_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P9_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P10_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_P10_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_计算值','67S0609212','上公差', 0.25) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('','F01_计算值','67S0609212','下公差', -0.125) SELECT* FROM dual



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PRO_NAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
SELECT count(*) from  MEASURE_EXT_DATA WHERE SCHEMA_ID='7EA74235B5E6492787CBCFC9315BC512' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PRO_NAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
SELECT count(*) from  MEASURE_EXT_DATA WHERE SCHEMA_ID='7EA74235B5E6492787CBCFC9315BC512' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_EXT_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 691
 INSERT ALL INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','上公差', 0.4) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','上公差', 0.25) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','下公差', -0.125) SELECT* FROM dual



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00904: "PRO_NAME": 标识符无效
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteReader(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, OracleDataReaderImpl& rdrImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[] scnForExecution, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, Int64& internalInitialLOBFS, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, IEnumerable`1 adrianParsedStmt, Boolean isDescribeOnly, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader(Boolean requery, Boolean fillRequest, CommandBehavior behavior)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteReader()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 665
SELECT count(*) from  MEASURE_EXT_DATA WHERE SCHEMA_ID='7EA74235B5E6492787CBCFC9315BC512' and PRO_Name='测试数据'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_EXT_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 691
 INSERT ALL INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P2_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P3_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P4_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P5_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P6_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P7_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P8_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P9_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P10_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P11_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_P12_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','上公差', 0.4) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','C01_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P2_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P3_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P4_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P5_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P6_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P7_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P8_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P9_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_P10_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','上公差', 0.25) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('7EA74235B5E6492787CBCFC9315BC512','F01_计算值','67S0609212','下公差', -0.125) SELECT* FROM dual



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='20240723155746931,67S0609212' and PRO_Name='测试数据'



System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 667
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='20240723155927388,67S0609212' and PRO_Name='测试数据'



