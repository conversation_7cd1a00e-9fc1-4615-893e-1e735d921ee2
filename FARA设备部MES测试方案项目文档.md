# FARA设备部MES测试方案项目文档

## 📋 项目概述

**FARA设备部MES测试方案**是一个基于C#/.NET技术栈的制造执行系统(MES)核心组件，专门用于工业生产线的设备测试、数据采集和质量管理。该项目集成了Oracle数据库、GraphQL API和Windows Forms界面，为生产制造提供完整的数字化解决方案。

### 🎯 核心价值
- **质量追溯**: 实现产品全生命周期的质量跟踪
- **工艺控制**: 确保生产工序标准化执行
- **数据驱动**: 实时采集和分析生产数据
- **人员管理**: 智能化的工位人员管理

---

## 🏗️ 项目架构总览

### 系统架构图

```mermaid
graph TB
    subgraph "用户界面层"
        A[母排绑定测试界面<br/>oracletest-母排绑定过程数据报工]
        B[过程数据记录界面<br/>processdatarecord]
    end
    
    subgraph "业务逻辑层"
        C[MES核心模块<br/>MES_ORACLE_DATABASE]
        D[序列号查询模块<br/>GetSNByMP]
    end
    
    subgraph "数据访问层"
        E[Oracle数据库<br/>***********:1521]
        F[GraphQL API<br/>api.mes-equip.efara.cn]
        G[MES服务<br/>mes-equip.efara.cn]
    end
    
    A --> C
    B --> C
    C --> E
    C --> G
    D --> F
    C --> D
    
    style A fill:#e1f5fe
    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
```

---

## 📁 详细目录结构

### 项目组织结构

```
📦 FARA设备部最新测试方案20250506/
├── 📂 GetSNByMP/                          # 母排序列号查询模块
│   ├── 📄 GetSNByMP.cs                    # GraphQL查询核心类
│   ├── 📄 GraphQlResult.cs                # GraphQL结果处理
│   └── 📄 GetSNByMP.csproj                # 项目配置文件
├── 📂 MES_ORACLE_DATABASE-20240606/       # MES数据库核心模块
│   ├── 📄 MES_ORACLE_DATABASE.cs          # 核心业务逻辑类
│   ├── 📄 App.config                      # 应用配置
│   └── 📄 MES_ORACLE_DATABASE.csproj      # 项目配置文件
├── 📂 oracletest-母排绑定过程数据报工/      # 母排绑定测试界面
│   ├── 📄 Form1.cs                        # 主窗体逻辑
│   ├── 📄 Form1.Designer.cs               # 窗体设计器代码
│   └── 📄 oracletest.csproj               # 项目配置文件
├── 📂 processdatarecord/                  # 过程数据记录模块
│   ├── 📄 Form1.cs                        # 数据记录窗体
│   └── 📄 processdatarecord.csproj        # 项目配置文件
├── 📂 packages/                           # NuGet包管理
│   └── 📂 Oracle.ManagedDataAccess.19.3.0/ # Oracle数据库驱动
└── 📄 oracletest.sln                      # 解决方案文件
```

---

## 🔧 核心模块详解

### 1. GetSNByMP - 母排序列号查询模块

#### 🎯 功能定位
专门负责通过母排编号(MP)查询对应的产品序列号信息，是产品追溯的关键入口。

#### 🔍 核心功能
- **GraphQL查询**: 使用现代化的GraphQL API进行数据查询
- **产品信息获取**: 返回完整的产品制造信息
- **异常处理**: 完善的错误处理和状态反馈机制

#### 📊 数据流程图

```mermaid
sequenceDiagram
    participant UI as 用户界面
    participant GSM as GetSNByMP
    participant API as GraphQL API
    participant DB as 数据库
    
    UI->>GSM: 输入母排编号(MP)
    GSM->>API: 发送GraphQL查询请求
    API->>DB: 查询产品绑定信息
    DB-->>API: 返回产品数据
    API-->>GSM: 返回JSON结果
    GSM->>GSM: 解析ProductSerial对象
    GSM-->>UI: 返回产品信息
```

#### 🏷️ 返回数据结构
```csharp
public class ProductSerial
{
    public string sn { get; set; }                    // 产品序列号
    public string partNo { get; set; }                // 物料号
    public string workOrderNo { get; set; }           // 工单号
    public string customerSeqNo { get; set; }         // 客户序号
    public DateTime productionDate { get; set; }      // 生产日期
    public string productionYearLast2 { get; set; }   // 生产年份后两位
    public string productionDayOfYear { get; set; }   // 年内第几天
}
```

### 2. MES_ORACLE_DATABASE - MES数据库核心模块

#### 🎯 功能定位
系统的核心业务逻辑模块，负责所有与MES系统相关的数据操作和业务流程控制。

#### 🔍 核心功能模块

##### 🔗 母排绑定功能
```csharp
public static BindProductAndRawMaterialResult BindProductAndRawMaterial(
    string StandardCode,    // 标准工序码
    string StationCode,     // 工位号
    string ProductSN,       // 成品编号
    string WO_CODE,         // 批次号
    string ComponentSN      // 母排序列号
)
```

##### 👥 人员管理功能
- **上岗人员查询**: 获取当前工位的操作人员信息
- **权限验证**: 确保只有上岗人员才能进行生产操作

##### 📊 数据记录功能
- **测试数据保存**: 保存详细的测试参数和结果
- **测试项目管理**: 管理测试项目清单和参数配置
- **质量数据追溯**: 建立完整的质量数据链

#### 🔄 业务流程图

```mermaid
flowchart TD
    A[扫描产品条码] --> B{条码解析}
    B -->|成功| C[获取产品信息]
    B -->|失败| D[错误提示]
    C --> E{工序验证}
    E -->|通过| F[检查上岗人员]
    E -->|不通过| G[工序状态错误]
    F --> H{人员验证}
    H -->|通过| I[执行生产操作]
    H -->|失败| J[请先上岗]
    I --> K[保存操作记录]
    K --> L[更新产品状态]
    L --> M[操作完成]
    
    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style M fill:#f1f8e9
    style D fill:#ffebee
    style G fill:#ffebee
    style J fill:#ffebee
```

### 3. oracletest-母排绑定过程数据报工 - 测试界面

#### 🎯 功能定位
提供用户友好的图形界面，用于母排绑定过程的测试和数据报工操作。

#### 🖥️ 界面功能
- **条码扫描测试**: 实时测试条码解析功能
- **性能监控**: 显示操作响应时间
- **数据报工**: 设备记录的保存和提交
- **状态显示**: 实时显示操作结果和系统状态

#### ⚡ 性能特性
```csharp
// 性能测试示例
Stopwatch sw = Stopwatch.StartNew();
sn = MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.DecodeSN(textBox1.Text);
sw.Stop();
label10.Text = sw.ElapsedMilliseconds.ToString() + "ms";
```

### 4. processdatarecord - 过程数据记录模块

#### 🎯 功能定位
专门用于生产过程中各种测试数据的记录和管理，支持多种测试项目的参数配置。

#### 📊 测试项目类型
- **电容测试**: C01_P1~P12_计算值
- **频率测试**: F01_P1~P12_计算值
- **其他专项测试**: 根据产品规格定制

#### 🔧 数据管理功能
- **测试项目清单管理**
- **测试参数配置** (标称值、上下偏差)
- **测试结果记录**
- **数据文件导出**

---

## 🔌 系统集成与接口

### 数据库连接配置

#### Oracle数据库
```xml
<connectionStrings>
    <add name="MES_Oracle"
         connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FARAMID)));Persist Security Info=True;User ID=SCADA_MES;Password=**********;connection timeout=2;" />
</connectionStrings>
```

#### API服务端点
- **GraphQL API**: `http://api.mes-equip.efara.cn/graphql`
- **MES服务**: `http://mes-equip.efara.cn`

### 🔄 数据流向图

```mermaid
graph LR
    subgraph "数据源"
        A[条码扫描器]
        B[测试设备]
        C[操作员输入]
    end

    subgraph "应用层"
        D[Windows Forms界面]
        E[业务逻辑层]
    end

    subgraph "数据层"
        F[Oracle数据库]
        G[GraphQL API]
        H[文件系统]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H

    style A fill:#fff3e0
    style B fill:#fff3e0
    style C fill:#fff3e0
    style D fill:#e8eaf6
    style E fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
```

---

## 📈 业务价值与应用场景

### 🎯 核心业务价值

#### 1. 质量追溯体系
- **全程追溯**: 从原材料到成品的完整追溯链
- **问题定位**: 快速定位质量问题的根源
- **责任追踪**: 明确每个环节的责任人

#### 2. 生产效率提升
- **自动化数据采集**: 减少人工录入错误
- **实时状态监控**: 及时发现和处理异常
- **标准化流程**: 确保操作的一致性

#### 3. 数据驱动决策
- **生产数据分析**: 支持生产优化决策
- **质量趋势分析**: 预防性质量管理
- **设备效率监控**: 优化设备利用率

### 🏭 典型应用场景

#### 场景1: 母排绑定作业
```mermaid
sequenceDiagram
    participant 操作员
    participant 扫描器
    participant MES系统
    participant 数据库

    操作员->>扫描器: 扫描母排条码
    扫描器->>MES系统: 传输条码数据
    MES系统->>数据库: 查询产品信息
    数据库-->>MES系统: 返回产品数据
    MES系统->>MES系统: 验证工序状态
    MES系统->>数据库: 保存绑定记录
    MES系统-->>操作员: 显示绑定结果
```

#### 场景2: 质量数据采集
```mermaid
flowchart LR
    A[测试设备] --> B[数据采集]
    B --> C{数据验证}
    C -->|合格| D[保存到数据库]
    C -->|不合格| E[质量预警]
    D --> F[生成报告]
    E --> G[异常处理]
    F --> H[数据分析]
    G --> H
```

---

## 🛠️ 技术特性与优势

### 💻 技术栈
- **开发语言**: C# (.NET Framework)
- **数据库**: Oracle Database
- **API技术**: GraphQL, REST API
- **界面技术**: Windows Forms
- **数据访问**: Oracle.ManagedDataAccess

### ⚡ 性能特性
- **高并发支持**: 支持多工位同时操作
- **实时响应**: 毫秒级的数据查询响应
- **数据一致性**: 事务性操作保证数据完整性
- **容错机制**: 完善的异常处理和恢复机制

### 🔒 安全特性
- **数据库连接安全**: 加密的数据库连接
- **操作权限控制**: 基于工位和人员的权限管理
- **数据备份**: 自动的数据备份和恢复机制
- **审计日志**: 完整的操作日志记录

---

## 📊 系统监控与维护

### 📈 关键性能指标(KPI)

| 指标类型 | 指标名称 | 目标值 | 监控方式 |
|---------|---------|--------|----------|
| 响应性能 | 条码解析时间 | < 100ms | 实时监控 |
| 数据质量 | 数据准确率 | > 99.9% | 定期检查 |
| 系统可用性 | 系统正常运行时间 | > 99.5% | 24/7监控 |
| 用户体验 | 操作成功率 | > 98% | 用户反馈 |

### 🔧 维护建议

#### 日常维护
- **数据库性能监控**: 定期检查数据库性能指标
- **日志文件管理**: 定期清理和归档日志文件
- **系统备份**: 每日自动备份关键数据

#### 定期维护
- **系统更新**: 定期更新系统补丁和安全更新
- **性能优化**: 根据使用情况优化数据库查询
- **容量规划**: 根据业务增长规划系统容量

---

## 🚀 未来发展规划

### 📋 短期目标 (3-6个月)
- **移动端支持**: 开发移动端应用支持
- **报表增强**: 增加更多业务报表功能
- **接口优化**: 优化API接口性能

### 🎯 中期目标 (6-12个月)
- **云端部署**: 支持云端部署和混合云架构
- **AI集成**: 集成机器学习算法进行质量预测
- **微服务架构**: 向微服务架构演进

### 🌟 长期愿景 (1-2年)
- **工业4.0集成**: 全面集成工业4.0技术栈
- **数字孪生**: 建立生产线数字孪生模型
- **智能制造**: 实现完全自动化的智能制造

---

## 📞 技术支持与联系方式

### 🏢 项目团队
- **项目负责人**: FARA设备部技术团队
- **开发团队**: MES系统开发组
- **运维团队**: IT运维支持组

### 📧 联系方式
- **技术支持**: 内部技术支持热线
- **系统维护**: IT运维部门
- **业务咨询**: 设备部业务团队

---

*本文档最后更新时间: 2025年1月*
*版本: v1.0*
*文档状态: 正式版*

