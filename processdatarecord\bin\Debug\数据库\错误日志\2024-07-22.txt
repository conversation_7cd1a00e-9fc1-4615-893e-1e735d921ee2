System.InvalidOperationException: 对已关闭对象的操作无效
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.IsDBNull(Int32 i)
   在 Oracle.ManagedDataAccess.Client.OracleDataReader.GetValue(Int32 i)
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.ReadString_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 644
Select SCHEMA_ID from MEASURE_SCHEMA where SCHEMA_NAME='123456,67S0609212'



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_EXT_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 668
 INSERT ALL INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','上公差', 0.4) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','上公差', 0.25) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','下公差', -0.125) SELECT* FROM dual



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_EXT_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 668
 INSERT ALL INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','上公差', 0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','上公差', 0.4) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','下公差', -0.2) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','上公差', 0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','下公差', -0.125) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','标准值', 0) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','上公差', 0.25) INTO MEASURE_EXT_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,EXT_NAME,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','下公差', -0.125) SELECT* FROM dual



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_SN_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 689
 INSERT ALL INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.233) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.23) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.171) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.094) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.088) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.044) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.032) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),-0.026) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.06) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),-0.037) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.466) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),-0.054) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.02) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.015) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.001) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.006) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.011) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),-0.029) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),-0.064) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:42','yyyy-MM-dd HH24:mi:ss'),0.137) SELECT* FROM dual



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_SN_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 689
 INSERT ALL INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.233) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.23) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.171) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.094) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.088) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.044) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.032) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),-0.026) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.06) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),-0.037) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.466) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),-0.054) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.02) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.015) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.001) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.006) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.011) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),-0.029) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),-0.064) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:26:49','yyyy-MM-dd HH24:mi:ss'),0.137) SELECT* FROM dual



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): ORA-00001: 违反唯一约束条件 (SCADA_MES.MEASURE_SN_DATA_UK)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.VerifyExecution(Int32& cursorId, Boolean bThrowArrayBindRelatedErrors, SqlStatementType sqlStatementType, Int32 arrayBindCount, OracleException& exceptionForArrayBindDML, Boolean& hasMoreRowsInDB, Boolean bFirstIterationDone)
   在 OracleInternal.ServiceObjects.OracleCommandImpl.ExecuteNonQuery(String commandText, OracleParameterCollection paramColl, CommandType commandType, OracleConnectionImpl connectionImpl, Int32 longFetchSize, Int64 clientInitialLOBFS, OracleDependencyImpl orclDependencyImpl, Int64[]& scnFromExecution, OracleParameterCollection& bindByPositionParamColl, Boolean& bBindParamPresent, OracleException& exceptionForArrayBindDML, OracleConnection connection, OracleLogicalTransaction& oracleLogicalTransaction, Boolean isFromEF)
   在 Oracle.ManagedDataAccess.Client.OracleCommand.ExecuteNonQuery()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.excutesql_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 689
 INSERT ALL INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.233) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.23) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.171) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.094) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.088) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.044) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.032) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),-0.026) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.06) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P11_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_P12_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),-0.037) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','C01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.466) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P1_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),-0.054) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P2_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.02) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P3_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.025) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P4_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.015) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P5_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.001) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P6_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.006) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P7_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.068) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P8_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.011) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P9_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),-0.029) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_P10_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),-0.064) INTO MEASURE_SN_DATA (SCHEMA_ID,SCHEMA_ITEM_NAME,BATCH_NO,SN,MEASURE_DT,VALUE) VALUES ('6CB8C1DD312F406A8F9278571677FC1B','F01_计算值','67S0609212','7Y14725654', TO_DATE('2024-07-22 14:27:22','yyyy-MM-dd HH24:mi:ss'),0.137) SELECT* FROM dual



