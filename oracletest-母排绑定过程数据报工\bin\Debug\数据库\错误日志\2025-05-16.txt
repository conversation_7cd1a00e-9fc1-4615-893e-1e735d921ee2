Oracle.ManagedDataAccess.Client.OracleException (0x80004005): 连接请求超时
   在 OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPwd, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassword, <PERSON>olean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, SecureString securedPassword, SecureString securedProxyPassword, OracleConnection connRefForCriteria)
   在 Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.Read_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\最新测试方案20250506\最新测试方案20250506\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 776
SELECT EMP_NAME,EMP_NO  FROM SCADA_MES.V_ON_STATION_T WHERE STATION_CODE='19201290049' AND OUT_DT is null and rownum=1 ORDER BY ONWORK_DT DESC



