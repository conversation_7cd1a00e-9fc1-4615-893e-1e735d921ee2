# FARA MES报工功能集成指南

## 📋 概述

本文档详细说明如何将FARA MES系统的报工功能集成到新系统中，包括必要条件、技术要求、集成流程和最佳实践。

---

## 🎯 报工功能核心架构

### 系统架构图

```mermaid
graph TB
    subgraph "新系统"
        A[新系统界面]
        B[报工适配层]
        C[数据转换模块]
    end
    
    subgraph "FARA MES核心"
        D[MESAutoLineClient]
        E[报工服务接口]
        F[工序管理]
        G[人员管理]
    end
    
    subgraph "数据层"
        H[Oracle数据库]
        I[MES服务器]
        J[GraphQL API]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> I
    D --> F
    D --> G
    F --> H
    G --> H
    D --> J
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#fff3e0
    style E fill:#e8f5e8
    style I fill:#e8f5e8
```

---

## 🔧 集成必要条件

### 1. 技术环境要求

#### 开发环境
- **.NET Framework**: 4.5 或更高版本
- **开发工具**: Visual Studio 2017 或更高版本
- **数据库驱动**: Oracle.ManagedDataAccess 19.3.0+

#### 运行环境
- **操作系统**: Windows Server 2012 R2 或更高版本
- **网络连接**: 能够访问MES服务器 (mes-equip.efara.cn)
- **数据库连接**: Oracle数据库访问权限

### 2. 依赖组件清单

| 组件名称 | 版本要求 | 用途 | 获取方式 |
|---------|---------|------|----------|
| MESAutoLineClient.dll | 最新版本 | MES客户端核心库 | FARA内部提供 |
| Oracle.ManagedDataAccess | 19.3.0+ | Oracle数据库连接 | NuGet包管理器 |
| Newtonsoft.Json | 12.0+ | JSON序列化 | NuGet包管理器 |
| System.ServiceModel | 4.0+ | WCF服务调用 | .NET Framework内置 |

### 3. 网络与安全要求

#### 网络连接
```xml
<!-- 必需的网络端点 -->
<endpoints>
    <endpoint name="MES服务器" url="http://mes-equip.efara.cn" port="80" />
    <endpoint name="GraphQL API" url="http://api.mes-equip.efara.cn/graphql" port="80" />
    <endpoint name="Oracle数据库" host="***********" port="1521" />
    <endpoint name="报工服务" url="http://mes-equip.efara.cn/report-work.svc" port="80" />
</endpoints>
```

#### 安全配置
- **数据库账户**: SCADA_MES (需要相应权限)
- **网络防火墙**: 开放相应端口访问权限
- **SSL证书**: 如需HTTPS访问，需配置相应证书

---

## 🚀 集成实施流程

### 阶段1: 环境准备 (1-2天)

#### 1.1 创建项目结构
```
新系统项目/
├── 📂 MESIntegration/           # MES集成模块
│   ├── 📄 MESClient.cs          # MES客户端封装
│   ├── 📄 ReportWorkAdapter.cs  # 报工适配器
│   └── 📄 DataConverter.cs      # 数据转换器
├── 📂 References/               # 引用库
│   ├── 📄 MESAutoLineClient.dll
│   └── 📄 Oracle.ManagedDataAccess.dll
└── 📂 Config/                   # 配置文件
    └── 📄 MESConfig.xml         # MES配置
```

#### 1.2 配置文件设置
```xml
<!-- MESConfig.xml -->
<configuration>
  <connectionStrings>
    <add name="MES_Oracle" 
         connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FARAMID)));Persist Security Info=True;User ID=SCADA_MES;Password=**********;connection timeout=2;" />
  </connectionStrings>
  
  <appSettings>
    <add key="MESServerUrl" value="http://mes-equip.efara.cn" />
    <add key="GraphQLApiUrl" value="http://api.mes-equip.efara.cn/graphql" />
    <add key="ReportWorkServiceUrl" value="http://mes-equip.efara.cn/report-work.svc" />
  </appSettings>
</configuration>
```

### 阶段2: 核心组件开发 (3-5天)

#### 2.1 MES客户端封装类
```csharp
using MESAutoLineClient;
using System;
using System.Configuration;

public class MESClientWrapper
{
    private readonly AutoLineClient _client;
    private static readonly Dictionary<string, string> _standardGroups = new Dictionary<string, string>();
    
    public MESClientWrapper()
    {
        string serverUrl = ConfigurationManager.AppSettings["MESServerUrl"];
        _client = AutoLineClient.Create(serverUrl);
        InitializeStandardGroups();
    }
    
    /// <summary>
    /// 初始化标准工序码字典
    /// </summary>
    private void InitializeStandardGroups()
    {
        if (_standardGroups.Count == 0)
        {
            var result = _client.GetStandardWorkGroupList();
            if (result.Success && result.Data.Length > 0)
            {
                foreach (var group in result.Data)
                {
                    _standardGroups[group.Name] = group.Code;
                }
            }
        }
    }
    
    /// <summary>
    /// 产品报工接口
    /// </summary>
    public ReportWorkResult ReportWork(ReportWorkRequest request)
    {
        try
        {
            // 1. 验证操作人员
            var workerInfo = GetWorkerInfo(request.StationCode);
            if (workerInfo == null)
            {
                return new ReportWorkResult 
                { 
                    Success = false, 
                    Message = "未查询到上岗人员信息，请先上岗再进行生产" 
                };
            }
            
            // 2. 获取标准工序码
            if (!_standardGroups.ContainsKey(request.ProcedureName))
            {
                return new ReportWorkResult 
                { 
                    Success = false, 
                    Message = $"未找到工序 {request.ProcedureName} 对应的标准工序码" 
                };
            }
            
            string procedureCode = _standardGroups[request.ProcedureName];
            
            // 3. 处理质量状态
            string qualityStatus = request.QualityResult == "合格" ? string.Empty : request.QualityResult;
            
            // 4. 调用MES报工接口
            var result = _client.ReportWorkByProduct(
                request.SerialNumber,    // 产品序列号
                request.StationCode,     // 工位码
                workerInfo.EmployeeNo,   // 操作者工号
                procedureCode,           // 标准工序码
                string.Empty,            // 产品状态
                qualityStatus,           // 错误类型
                qualityStatus            // 错误描述
            );
            
            return new ReportWorkResult
            {
                Success = result.Success,
                Message = result.Message
            };
        }
        catch (Exception ex)
        {
            return new ReportWorkResult
            {
                Success = false,
                Message = $"报工异常: {ex.Message}"
            };
        }
    }
    
    /// <summary>
    /// 获取工位操作人员信息
    /// </summary>
    private WorkerInfo GetWorkerInfo(string stationCode)
    {
        try
        {
            var result = _client.QueryCurrentOnWorkListByStationCode(stationCode);
            if (result.Success && result.Data.Length > 0)
            {
                var latestWorker = result.Data
                    .OrderByDescending(x => x.OnWorkDate)
                    .First();
                    
                return new WorkerInfo
                {
                    EmployeeNo = latestWorker.EmployeeNo,
                    EmployeeName = latestWorker.EmployeeName
                };
            }
        }
        catch (Exception ex)
        {
            // 记录日志
            Console.WriteLine($"获取工人信息失败: {ex.Message}");
        }
        
        return null;
    }
}
```

#### 2.2 数据模型定义
```csharp
/// <summary>
/// 报工请求数据模型
/// </summary>
public class ReportWorkRequest
{
    public string SerialNumber { get; set; }      // 产品序列号
    public string StationCode { get; set; }       // 工位码
    public string ProcedureName { get; set; }     // 工序名称
    public string QualityResult { get; set; }     // 质量结果 ("合格"/"不合格"/具体不良描述)
    public string EquipmentNumber { get; set; }   // 设备编号
    public DateTime ReportTime { get; set; }      // 报工时间
}

/// <summary>
/// 报工结果数据模型
/// </summary>
public class ReportWorkResult
{
    public bool Success { get; set; }             // 是否成功
    public string Message { get; set; }           // 结果消息
    public string TransactionId { get; set; }     // 事务ID（可选）
}

/// <summary>
/// 工人信息数据模型
/// </summary>
public class WorkerInfo
{
    public string EmployeeNo { get; set; }        // 工号
    public string EmployeeName { get; set; }      // 姓名
}
```

### 阶段3: 业务逻辑集成 (2-3天)

#### 3.1 报工业务流程实现
```csharp
public class ReportWorkService
{
    private readonly MESClientWrapper _mesClient;
    private readonly ILogger _logger;
    
    public ReportWorkService(ILogger logger)
    {
        _mesClient = new MESClientWrapper();
        _logger = logger;
    }
    
    /// <summary>
    /// 执行报工操作
    /// </summary>
    public async Task<ReportWorkResult> ExecuteReportWorkAsync(ReportWorkRequest request)
    {
        _logger.Info($"开始报工: SN={request.SerialNumber}, 工位={request.StationCode}");
        
        try
        {
            // 1. 数据验证
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return new ReportWorkResult 
                { 
                    Success = false, 
                    Message = validationResult.ErrorMessage 
                };
            }
            
            // 2. 产品状态检查
            var productValid = await CheckProductStatusAsync(request.SerialNumber, request.ProcedureName);
            if (!productValid)
            {
                return new ReportWorkResult 
                { 
                    Success = false, 
                    Message = "产品状态不符合当前工序要求" 
                };
            }
            
            // 3. 执行报工
            var result = _mesClient.ReportWork(request);
            
            // 4. 记录日志
            _logger.Info($"报工完成: SN={request.SerialNumber}, 结果={result.Success}, 消息={result.Message}");
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.Error($"报工异常: {ex}");
            return new ReportWorkResult 
            { 
                Success = false, 
                Message = $"系统异常: {ex.Message}" 
            };
        }
    }
    
    /// <summary>
    /// 验证报工请求数据
    /// </summary>
    private ValidationResult ValidateRequest(ReportWorkRequest request)
    {
        if (string.IsNullOrEmpty(request.SerialNumber))
            return new ValidationResult { IsValid = false, ErrorMessage = "产品序列号不能为空" };
            
        if (string.IsNullOrEmpty(request.StationCode))
            return new ValidationResult { IsValid = false, ErrorMessage = "工位码不能为空" };
            
        if (string.IsNullOrEmpty(request.ProcedureName))
            return new ValidationResult { IsValid = false, ErrorMessage = "工序名称不能为空" };
            
        return new ValidationResult { IsValid = true };
    }
    
    /// <summary>
    /// 检查产品状态
    /// </summary>
    private async Task<bool> CheckProductStatusAsync(string serialNumber, string procedureName)
    {
        try
        {
            // 调用MES系统的产品状态检查接口
            return _mesClient.CheckProductStatus(serialNumber, procedureName);
        }
        catch (Exception ex)
        {
            _logger.Error($"产品状态检查异常: {ex}");
            return false;
        }
    }
}
```

### 阶段4: 用户界面集成 (1-2天)

#### 4.1 报工界面示例
```csharp
public partial class ReportWorkForm : Form
{
    private readonly ReportWorkService _reportWorkService;

    public ReportWorkForm()
    {
        InitializeComponent();
        _reportWorkService = new ReportWorkService(new ConsoleLogger());
    }

    private async void btnReportWork_Click(object sender, EventArgs e)
    {
        try
        {
            // 禁用按钮防止重复提交
            btnReportWork.Enabled = false;

            var request = new ReportWorkRequest
            {
                SerialNumber = txtSerialNumber.Text.Trim(),
                StationCode = txtStationCode.Text.Trim(),
                ProcedureName = cmbProcedure.Text,
                QualityResult = rbQualified.Checked ? "合格" : txtDefectDescription.Text,
                EquipmentNumber = txtEquipmentNumber.Text.Trim(),
                ReportTime = DateTime.Now
            };

            var result = await _reportWorkService.ExecuteReportWorkAsync(request);

            if (result.Success)
            {
                MessageBox.Show("报工成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ClearForm();
            }
            else
            {
                MessageBox.Show($"报工失败：{result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"系统异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnReportWork.Enabled = true;
        }
    }

    private void ClearForm()
    {
        txtSerialNumber.Clear();
        txtDefectDescription.Clear();
        rbQualified.Checked = true;
    }
}
```

---

## 📊 集成验证与测试

### 测试用例设计

#### 1. 功能测试用例

| 测试场景 | 输入数据 | 预期结果 | 验证点 |
|---------|---------|---------|--------|
| 正常报工 | 有效SN + 工位码 + 工序 | 报工成功 | MES系统记录更新 |
| 无效序列号 | 不存在的SN | 报工失败 | 错误提示准确 |
| 工序不匹配 | SN与工序不符 | 报工失败 | 工序验证生效 |
| 未上岗操作 | 工位无上岗人员 | 报工失败 | 人员验证生效 |
| 网络异常 | 网络中断 | 报工失败 | 异常处理正确 |

#### 2. 集成测试脚本
```csharp
[TestClass]
public class ReportWorkIntegrationTests
{
    private ReportWorkService _service;

    [TestInitialize]
    public void Setup()
    {
        _service = new ReportWorkService(new TestLogger());
    }

    [TestMethod]
    public async Task ReportWork_ValidData_ShouldSucceed()
    {
        // Arrange
        var request = new ReportWorkRequest
        {
            SerialNumber = "7Y14725654",
            StationCode = "ST001",
            ProcedureName = "测试工序",
            QualityResult = "合格"
        };

        // Act
        var result = await _service.ExecuteReportWorkAsync(request);

        // Assert
        Assert.IsTrue(result.Success);
        Assert.IsNotNull(result.Message);
    }

    [TestMethod]
    public async Task ReportWork_InvalidSerialNumber_ShouldFail()
    {
        // Arrange
        var request = new ReportWorkRequest
        {
            SerialNumber = "INVALID_SN",
            StationCode = "ST001",
            ProcedureName = "测试工序",
            QualityResult = "合格"
        };

        // Act
        var result = await _service.ExecuteReportWorkAsync(request);

        // Assert
        Assert.IsFalse(result.Success);
        Assert.IsTrue(result.Message.Contains("序列号"));
    }
}
```

---

## 🔍 故障排查指南

### 常见问题与解决方案

#### 1. 连接问题
**问题**: 无法连接到MES服务器
```
错误信息: "无法连接到远程服务器"
```
**解决方案**:
1. 检查网络连接: `ping mes-equip.efara.cn`
2. 验证防火墙设置
3. 确认服务器地址配置正确
4. 检查代理设置

#### 2. 认证问题
**问题**: 数据库连接失败
```
错误信息: "ORA-01017: invalid username/password"
```
**解决方案**:
1. 验证数据库连接字符串
2. 确认用户名密码正确
3. 检查数据库用户权限
4. 测试数据库连通性

#### 3. 业务逻辑问题
**问题**: 报工失败 - 工序验证不通过
```
错误信息: "产品状态不符合当前工序要求"
```
**解决方案**:
1. 检查产品当前状态
2. 验证工序流程配置
3. 确认标准工序码映射
4. 检查产品工艺路线

---

## 📈 性能优化建议

### 1. 连接池优化
```csharp
public class MESConnectionPool
{
    private static readonly ConcurrentQueue<AutoLineClient> _clientPool = new ConcurrentQueue<AutoLineClient>();
    private static readonly object _lock = new object();
    private const int MaxPoolSize = 10;

    public static AutoLineClient GetClient()
    {
        if (_clientPool.TryDequeue(out AutoLineClient client))
        {
            return client;
        }

        lock (_lock)
        {
            return AutoLineClient.Create(ConfigurationManager.AppSettings["MESServerUrl"]);
        }
    }

    public static void ReturnClient(AutoLineClient client)
    {
        if (_clientPool.Count < MaxPoolSize)
        {
            _clientPool.Enqueue(client);
        }
        else
        {
            client?.Dispose();
        }
    }
}
```

### 2. 缓存策略
```csharp
public class MESDataCache
{
    private static readonly MemoryCache _cache = new MemoryCache("MESCache");
    private static readonly TimeSpan DefaultExpiry = TimeSpan.FromMinutes(30);

    public static T Get<T>(string key) where T : class
    {
        return _cache.Get(key) as T;
    }

    public static void Set<T>(string key, T value, TimeSpan? expiry = null)
    {
        _cache.Set(key, value, DateTimeOffset.Now.Add(expiry ?? DefaultExpiry));
    }

    // 缓存标准工序码
    public static string GetStandardGroupCode(string procedureName)
    {
        string cacheKey = $"StandardGroup_{procedureName}";
        return Get<string>(cacheKey);
    }

    public static void CacheStandardGroupCode(string procedureName, string code)
    {
        string cacheKey = $"StandardGroup_{procedureName}";
        Set(cacheKey, code, TimeSpan.FromHours(24));
    }
}
```

---

## 🚀 部署与上线

### 部署检查清单

#### 环境准备
- [ ] .NET Framework 4.5+ 已安装
- [ ] Oracle客户端已配置
- [ ] 网络连接已测试
- [ ] 防火墙规则已配置
- [ ] 数据库权限已分配

#### 配置验证
- [ ] 连接字符串正确
- [ ] 服务端点可访问
- [ ] 日志路径可写入
- [ ] 缓存目录已创建

#### 功能测试
- [ ] 基本报工功能正常
- [ ] 异常处理正确
- [ ] 性能指标达标
- [ ] 日志记录完整

### 上线步骤

1. **预生产环境验证** (1天)
   - 部署到预生产环境
   - 执行完整测试用例
   - 性能压力测试
   - 数据一致性验证

2. **生产环境部署** (半天)
   - 选择业务低峰期
   - 备份现有系统
   - 部署新版本
   - 验证核心功能

3. **监控与观察** (1周)
   - 实时监控系统状态
   - 收集用户反馈
   - 分析性能数据
   - 及时处理问题

---

## 📞 技术支持

### 联系方式
- **技术支持**: FARA设备部MES团队
- **紧急联系**: 7x24小时技术热线
- **文档更新**: 定期更新集成指南

### 支持范围
- 集成技术咨询
- 问题排查协助
- 性能优化建议
- 版本升级支持

---

*本文档版本: v2.0*
*最后更新: 2025年1月*
*适用范围: FARA MES报工功能集成*
