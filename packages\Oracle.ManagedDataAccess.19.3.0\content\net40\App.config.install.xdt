<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections xdt:Transform="InsertIfMissing">
  </configSections> 

  <configSections xdt:Transform="InsertBefore(/configuration/*[1])">
  </configSections>   

  <!-- remove existing entry -->
  <configSections xdt:Locator="XPath(/configuration/configSections[last()])">
      <section name="oracle.manageddataaccess.client" xdt:Transform="Remove" xdt:Locator="Match(name)" />
  </configSections>

  <!-- insert new entry -->
  <configSections xdt:Locator="XPath(/configuration/configSections[last()])">
      <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" xdt:Transform="Insert" />
  </configSections>

  <configSections xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

  <!-- If system.data tag is absent -->
  <system.data xdt:Transform="InsertIfMissing">
    <DbProviderFactories>
    </DbProviderFactories>
  </system.data>

  <!-- If system.data tag is present, but DbProviderFactories tag is absent -->
  <system.data>
    <DbProviderFactories xdt:Transform="InsertIfMissing">
    </DbProviderFactories>
  </system.data>
  
  <!-- remove existing ODPM entry -->  
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" xdt:Transform="Remove" xdt:Locator="Match(invariant)" />
      <add name="ODP.NET, Managed Driver" xdt:Transform="Remove" xdt:Locator="Match(name)" />      
    </DbProviderFactories>
  </system.data>

  <!-- add new ODPM entry -->
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" xdt:Transform="Insert"/>
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" xdt:Transform="Insert" />      
    </DbProviderFactories>
  </system.data>

  <!-- If runtime tag is absent -->
  <runtime xdt:Transform="InsertIfMissing">
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
    </assemblyBinding>
  </runtime>

  <!-- If runtime tag is present, but assembly binding tag is absent -->
  <runtime>
    <assemblyBinding xdt:Transform="InsertIfMissing" xmlns="urn:schemas-microsoft-com:asm.v1">
    </assemblyBinding>
  </runtime>

  <!-- remove existing entry -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly xdt:Transform="Remove"
          xdt:Locator="Condition(./_defaultNamespace:assemblyIdentity/@name='Oracle.ManagedDataAccess')" >
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

  <!-- insert new entry -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly xdt:Transform="Insert">
        <publisherPolicy apply="no"/>
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
        <bindingRedirect oldVersion="4.121.0.0 - 4.65535.65535.65535" newVersion="**********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

  <!-- If oracle.manageddataaccess.client tag is absent -->
  <oracle.manageddataaccess.client xdt:Transform="InsertIfMissing">
    <version number="*">
      <dataSources>
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>

  <!-- If version tag is absent -->
  <oracle.manageddataaccess.client>
    <version number="*" xdt:Transform="InsertIfMissing">
      <dataSources>
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>

  <!-- If dataSources tag is absent -->
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources xdt:Transform="InsertIfMissing">         
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>

  <!-- add entry if missing -->
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources>
         <dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " xdt:Transform="InsertIfMissing" xdt:Locator="Match(alias)"/>
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>

</configuration>
