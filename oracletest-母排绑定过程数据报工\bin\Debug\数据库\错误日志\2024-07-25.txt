Oracle.ManagedDataAccess.Client.OracleException (0x80004005): 网络传输: TCP 传输地址连接故障 ---> OracleInternal.Network.NetworkException (0x80004005): 网络传输: TCP 传输地址连接故障 ---> System.Net.Sockets.SocketException (0x80004005): 套接字操作尝试一个无法连接的主机。 ***********:1521
   在 System.Net.Sockets.Socket.InternalEndConnect(IAsyncResult asyncResult)
   在 System.Net.Sockets.Socket.EndConnect(IAsyncResult asyncResult)
   在 OracleInternal.Network.TcpTransportAdapter.ConnectIterate()
   在 OracleInternal.Network.OracleCommunication.DoConnect(String tnsDescriptor)
   在 OracleInternal.Network.OracleCommunication.Connect(String tnsDescriptor, Boolean doNAHandshake, String IName, ConnectionOption CO)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.Connect(ConnectionString cs, Boolean bOpenEndUserSession, OracleConnection connRefForCriteria, String instanceName)
   在 OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPwd, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassword, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, SecureString securedPassword, SecureString securedProxyPassword, OracleConnection connRefForCriteria)
   在 Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.Read_mes(String sql)
SELECT EMP_NAME,EMP_NO  FROM SCADA_MES.V_ON_STATION_T WHERE STATION_CODE='19201290022' AND OUT_DT is null and rownum=1 ORDER BY ONWORK_DT DESC



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): 连接请求超时
   在 OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPwd, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassword, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, SecureString securedPassword, SecureString securedProxyPassword, OracleConnection connRefForCriteria)
   在 Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.Read_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 702
SELECT EMP_NAME,EMP_NO  FROM SCADA_MES.V_ON_STATION_T WHERE STATION_CODE='19201290049' AND OUT_DT is null and rownum=1 ORDER BY ONWORK_DT DESC



Oracle.ManagedDataAccess.Client.OracleException (0x80004005): 网络传输: TCP 传输地址连接故障 ---> OracleInternal.Network.NetworkException (0x80004005): 网络传输: TCP 传输地址连接故障 ---> System.Net.Sockets.SocketException (0x80004005): 套接字操作尝试一个无法连接的主机。 ***********:1521
   在 System.Net.Sockets.Socket.InternalEndConnect(IAsyncResult asyncResult)
   在 System.Net.Sockets.Socket.EndConnect(IAsyncResult asyncResult)
   在 OracleInternal.Network.TcpTransportAdapter.ConnectIterate()
   在 OracleInternal.Network.OracleCommunication.DoConnect(String tnsDescriptor)
   在 OracleInternal.Network.OracleCommunication.Connect(String tnsDescriptor, Boolean doNAHandshake, String IName, ConnectionOption CO)
   在 OracleInternal.ServiceObjects.OracleConnectionImpl.Connect(ConnectionString cs, Boolean bOpenEndUserSession, OracleConnection connRefForCriteria, String instanceName)
   在 OracleInternal.ConnectionPool.PoolManager`3.Get(ConnectionString csWithDiffOrNewPwd, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OraclePoolManager.Get(ConnectionString csWithNewPassword, Boolean bGetForApp, OracleConnection connRefForCriteria, String affinityInstanceName, Boolean bForceMatch)
   在 OracleInternal.ConnectionPool.OracleConnectionDispenser`3.Get(ConnectionString cs, PM conPM, ConnectionString pmCS, SecureString securedPassword, SecureString securedProxyPassword, OracleConnection connRefForCriteria)
   在 Oracle.ManagedDataAccess.Client.OracleConnection.Open()
   在 MES_ORACLE_DATABASE.MES_ORACLE_DATABASE.Read_mes(String sql) 位置 E:\4.代码\7.七厂\oracle\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.cs:行号 702
SELECT EMP_NAME,EMP_NO  FROM SCADA_MES.V_ON_STATION_T WHERE STATION_CODE='19201290049' AND OUT_DT is null and rownum=1 ORDER BY ONWORK_DT DESC



