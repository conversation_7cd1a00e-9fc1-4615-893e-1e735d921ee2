<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <!-- remove existing entry -->
  <configSections>
      <section name="oracle.manageddataaccess.client" xdt:Transform="Remove" xdt:Locator="Match(name)" />
  </configSections>

  <!-- remove if section is empty -->
  <configSections xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

  <!-- remove existing entry -->
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" xdt:Transform="Remove" xdt:Locator="Match(invariant)" />
      <add name="ODP.NET, Managed Driver" xdt:Transform="Remove" xdt:Locator="Match(name)" />      
    </DbProviderFactories>
  </system.data>

  <!-- remove if section is empty -->
  <system.data>
    <DbProviderFactories xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />
  </system.data>
  <system.data xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

  <!-- remove existing entry -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly xdt:Transform="Remove"
          xdt:Locator="Condition(./_defaultNamespace:assemblyIdentity/@name='Oracle.ManagedDataAccess')" >
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <!-- remove if section is empty -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1" xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />
  </runtime>
  <runtime xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

  <!-- remove existing entry -->
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources>
        <dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " xdt:Transform="Remove" xdt:Locator="Match(alias)"/>
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>

  <!-- remove if section is empty -->
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />
    </version>
  </oracle.manageddataaccess.client>

  <!-- remove existing entry -->
  <oracle.manageddataaccess.client>
    <version number="*" xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />
  </oracle.manageddataaccess.client>

  <oracle.manageddataaccess.client xdt:Transform="RemoveAll" xdt:Locator="Condition(count(*)=0)" />

</configuration>
